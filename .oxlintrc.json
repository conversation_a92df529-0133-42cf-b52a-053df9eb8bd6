{
	"$schema": "./node_modules/oxlint/configuration_schema.json",
	"env": {
		"browser": true
	},
	"ignorePatterns": ["src/locale/**", "**.gen.ts"],
	"categories": {
		"correctness": "error",
		"suspicious": "warn",
		"pedantic": "warn",
		"perf": "warn",
		"style": "warn",
		"restriction": "warn",
		"nursery": "warn"
	},
	"plugins": [
		"import",
		"jsdoc",
		"jsx-a11y",
		"oxc",
		"promise",
		"typescript",
		"unicorn"
	],
	"settings": {
		"jsx-a11y": {
			"attributes": {
				"for": ["for"]
			},
			"polymorphicPropName": "as"
		}
	},

	"rules": {
		"array-callback-return": [
			"warn",
			{
				"allowImplicitReturn": true
			}
		],
		"arrow-body-style": "off",
		"curly": "off",
		"eqeqeq": ["off", "smart"],
		"func-style": [
			"error",
			"declaration",
			{
				"allowArrowFunctions": true
			}
		],
		"id-length": "off",
		"init-declarations": "off",
		"max-depth": ["warn", 4],
		"max-lines": [
			"warn",
			{
				"max": 300,
				"skipBlankLines": true,
				"skipComments": true
			}
		],
		"max-lines-per-function": [
			"warn",
			{
				"IIFEs": false,
				"max": 100,
				"skipBlankLines": true,
				"skipComments": true
			}
		],
		"max-nested-callbacks": ["warn", 10],
		"max-params": [
			"warn",
			{
				"max": 4
			}
		],
		"new-cap": "off",
		"no-async-await": "off",
		"no-continue": "off",
		// Disable this, as type import is not recognized.
		// We use the import plugin instead.
		"no-duplicate-imports": "off",
		"no-empty-function": [
			"warn",
			{
				"allow": ["constructor", "privateConstructors", "protectedConstructors"]
			}
		],
		"no-magic-numbers": [
			"error",
			{
				"ignore": [-1, 0, 1, 2, 4, 8, 12, 16, 24, 60, 1000, 3600],
				"ignoreArrayIndexes": true
			}
		],
		"no-plusplus": ["warn", { "allowForLoopAfterthoughts": true }],
		"no-ternary": "off",
		"no-undefined": "off",
		"no-unused-vars": "warn",
		"no-void": "off",
		"prefer-destructuring": "off",
		"sort-imports": "off", // We use prettier plugin
		"sort-keys": "off",

		"import/exports-last": "off",
		"import/group-exports": "off",
		"import/max-dependencies": "off",
		"import/no-anonymous-default-export": "off",
		"import/no-cycle": "off",
		"import/no-namespace": "off",
		"import/no-unassigned-imports": ["error", { "allow": ["**/*.css"] }],
		"import/prefer-default-export": "off",

		"oxc/no-barrel-file": "off",
		"oxc/no-const-enum": "off",
		"oxc/no-optional-chaining": "off",
		"oxc/no-rest-spread-properties": "off",

		"promise/prefer-await-to-then": "off",

		"typescript/ban-ts-comment": "off",
		"typescript/consistent-type-definitions": "off",
		"typescript/explicit-function-return-type": "off",
		"typescript/explicit-module-boundary-types": "off",
		"typescript/no-non-null-assertion": "off",
		"typescript/prefer-enum-initializers": "off",

		"unicorn/explicit-length-check": "off",
		"unicorn/filename-case": [
			"warn",
			{
				"cases": {
					"camelCase": true,
					"snakeCase": true,
					"pascalCase": true
				}
			}
		],
		"unicorn/no-anonymous-default-export": "off",
		"unicorn/prefer-spread": "off"
	},
	"overrides": [
		{
			"files": ["*.stories.tsx"],
			"rules": {
				"no-magic-numbers": "off",
				"import/no-default-export": "off"
			}
		},
		{
			"files": ["*.test.ts", "*.test.tsx"],
			"plugins": ["vitest"],
			"rules": {
				"max-lines-per-function": "off",
				"vitest/prefer-to-be-truthy": "off"
			}
		}
	]
}
