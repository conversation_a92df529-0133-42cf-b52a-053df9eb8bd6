/**
 * TypeScript definitions for CropperJS v2
 * Generated from API documentation at https://fengyuanchen.github.io/cropperjs/v2/api/
 */

// Base types and enums
export type ShadowRootMode = 'open' | 'closed';

export type ActionType = 
  | 'select' 
  | 'move' 
  | 'scale' 
  | 'rotate' 
  | 'transform' 
  | 'n-resize' 
  | 'e-resize' 
  | 's-resize' 
  | 'w-resize' 
  | 'ne-resize' 
  | 'nw-resize' 
  | 'se-resize' 
  | 'sw-resize' 
  | 'none';

export type InitialCenterSize = 'contain' | 'cover';

export type ResizeDirection = 'both' | 'horizontal' | 'vertical' | 'none';

export type TransformationMatrix = [number, number, number, number, number, number];

// Selection interface
export interface Selection {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Event detail interfaces
export interface ActionEventDetail {
  action: ActionType;
  relatedEvent: PointerEvent | TouchEvent | MouseEvent | WheelEvent;
  scale?: number;
  rotate?: number;
  startX?: number;
  startY?: number;
  endX?: number;
  endY?: number;
}

export interface TransformEventDetail {
  matrix: TransformationMatrix;
  oldMatrix: TransformationMatrix;
}

export interface ChangeEventDetail extends Selection {}

// Canvas options
export interface CanvasOptions {
  width?: number;
  height?: number;
  beforeDraw?: (context: CanvasRenderingContext2D, canvas: HTMLCanvasElement) => void;
}

// Cropper options
export interface CropperOptions {
  container?: Element | string;
  template?: string;
}

// Base CropperElement interface
export interface CropperElement extends HTMLElement {
  // Properties
  shadowRootMode: ShadowRootMode;
  slottable: boolean;
  themeColor: string;

  // Methods
  $getShadowRoot(): ShadowRoot;
  $addStyles(styles: string): CSSStyleSheet | HTMLStyleElement;
  $emit(type: string, detail?: unknown, options?: CustomEventInit): boolean;
  $nextTick(callback?: Function): Promise<void>;

  // Static properties
  static readonly $name: string;
  static readonly $version: string;

  // Static methods
  static $define(name?: string, options?: ElementDefinitionOptions): void;
}

// CropperCanvas interface
export interface CropperCanvas extends CropperElement {
  // Properties
  background: boolean;
  disabled: boolean;
  scaleStep: number;
  themeColor: string;

  // Methods
  $setAction(action: ActionType): CropperCanvas;
  $toCanvas(options?: CanvasOptions): Promise<HTMLCanvasElement>;

  // Events
  addEventListener(type: 'action', listener: (event: CustomEvent<ActionEventDetail>) => void): void;
  addEventListener(type: 'actionstart', listener: (event: CustomEvent<ActionEventDetail>) => void): void;
  addEventListener(type: 'actionmove', listener: (event: CustomEvent<ActionEventDetail>) => void): void;
  addEventListener(type: 'actionend', listener: (event: CustomEvent<ActionEventDetail>) => void): void;
}

// CropperImage interface
export interface CropperImage extends CropperElement {
  // Properties
  initialCenterSize: InitialCenterSize;
  rotatable: boolean;
  scalable: boolean;
  skewable: boolean;
  slottable: boolean;
  translatable: boolean;

  // Inherited img attributes
  alt: string;
  crossOrigin: string | null;
  decoding: string;
  loading: string;
  referrerPolicy: string;
  sizes: string;
  src: string;
  srcset: string;

  // Methods
  $ready(callback?: (image: HTMLImageElement) => void): Promise<HTMLImageElement>;
  $center(size?: InitialCenterSize): CropperImage;
  $move(x: number, y?: number): CropperImage;
  $moveTo(x: number, y?: number): CropperImage;
  $rotate(angle: number | string, x?: number, y?: number): CropperImage;
  $zoom(scale: number, x?: number, y?: number): CropperImage;
  $scale(x: number, y?: number): CropperImage;
  $skew(x: number | string, y?: number | string): CropperImage;
  $translate(x: number, y?: number): CropperImage;
  $transform(a: number, b: number, c: number, d: number, e: number, f: number): CropperImage;
  $setTransform(a: number | TransformationMatrix, b?: number, c?: number, d?: number, e?: number, f?: number): CropperImage;
  $getTransform(): TransformationMatrix;
  $resetTransform(): CropperImage;

  // Events
  addEventListener(type: 'transform', listener: (event: CustomEvent<TransformEventDetail>) => void): void;
}

// CropperShade interface
export interface CropperShade extends CropperElement {
  // Properties
  x: number;
  y: number;
  width: number;
  height: number;
  slottable: boolean;
  themeColor: string;

  // Methods
  $change(x: number, y: number, width?: number, height?: number): CropperShade;
  $reset(): CropperShade;
  $render(): CropperShade;
}

// CropperHandle interface
export interface CropperHandle extends CropperElement {
  // Properties
  action: ActionType;
  plain: boolean;
  slottable: boolean;
  themeColor: string;
}

// CropperSelection interface
export interface CropperSelection extends CropperElement, Selection {
  // Properties
  aspectRatio: number;
  initialAspectRatio: number;
  initialCoverage: number;
  dynamic: boolean;
  movable: boolean;
  resizable: boolean;
  zoomable: boolean;
  multiple: boolean;
  keyboard: boolean;
  outlined: boolean;
  precise: boolean;

  // Methods
  $center(): CropperSelection;
  $move(x: number, y?: number): CropperSelection;
  $moveTo(x: number, y?: number): CropperSelection;
  $resize(action: ActionType, offsetX?: number, offsetY?: number, aspectRatio?: number): CropperSelection;
  $zoom(scale: number, x?: number, y?: number): CropperSelection;
  $change(x: number, y: number, width?: number, height?: number, aspectRatio?: number): CropperSelection;
  $reset(): CropperSelection;
  $clear(): CropperSelection;
  $render(): CropperSelection;
  $toCanvas(options?: CanvasOptions): Promise<HTMLCanvasElement>;

  // Events
  addEventListener(type: 'change', listener: (event: CustomEvent<ChangeEventDetail>) => void): void;
}

// CropperGrid interface
export interface CropperGrid extends CropperElement {
  // Properties
  rows: number;
  columns: number;
  bordered: boolean;
  covered: boolean;
  slottable: boolean;
  themeColor: string;
}

// CropperCrosshair interface
export interface CropperCrosshair extends CropperElement {
  // Properties
  centered: boolean;
  slottable: boolean;
  themeColor: string;
}

// CropperViewer interface
export interface CropperViewer extends CropperElement {
  // Properties
  resize: ResizeDirection;
  selection: string;
  slottable: boolean;
}

// Main Cropper class
export interface Cropper {
  // Properties
  element: HTMLImageElement | HTMLCanvasElement;
  options: CropperOptions;
  container: Element;

  // Methods
  getCropperCanvas(): CropperCanvas | null;
  getCropperImage(): CropperImage | null;
  getCropperSelection(): CropperSelection | null;
  getCropperSelections(): NodeListOf<CropperSelection> | null;
}

// Constructor interface
export interface CropperConstructor {
  new (element: HTMLImageElement | HTMLCanvasElement | string, options?: CropperOptions): Cropper;
}

// Default template constant
export declare const DEFAULT_TEMPLATE: string;

// Main export
declare const Cropper: CropperConstructor;
export default Cropper;

// Named exports
export {
  CropperElement,
  CropperCanvas,
  CropperImage,
  CropperShade,
  CropperHandle,
  CropperSelection,
  CropperGrid,
  CropperCrosshair,
  CropperViewer,
};
